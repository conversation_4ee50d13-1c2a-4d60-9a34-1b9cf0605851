
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  role: 'role',
  deletedAt: 'deletedAt',
  googleId: 'googleId',
  googleAccessToken: 'googleAccessToken',
  googleRefreshToken: 'googleRefreshToken',
  appleId: 'appleId',
  appleRefreshToken: 'appleRefreshToken',
  appleEmail: 'appleEmail',
  appleGivenName: 'appleGivenName',
  appleFamilyName: 'appleFamilyName',
  profilePictureUrl: 'profilePictureUrl',
  provider: 'provider'
};

exports.Prisma.OtpCodeScalarFieldEnum = {
  id: 'id',
  email: 'email',
  code: 'code',
  type: 'type',
  expiresAt: 'expiresAt',
  used: 'used',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  planName: 'planName',
  price: 'price',
  durationInDays: 'durationInDays',
  features: 'features',
  userId: 'userId',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripeCustomerId: 'stripeCustomerId',
  isTrial: 'isTrial',
  trialStartedAt: 'trialStartedAt',
  trialEndsAt: 'trialEndsAt',
  trialConvertedToPaid: 'trialConvertedToPaid',
  trialCancellationReason: 'trialCancellationReason'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  coursePreviewVideoUrl: 'coursePreviewVideoUrl',
  isFree: 'isFree',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  imageUrl: 'imageUrl',
  averageRating: 'averageRating',
  reviewCount: 'reviewCount',
  price: 'price',
  currency: 'currency',
  stripeProductId: 'stripeProductId',
  stripePriceId: 'stripePriceId',
  categoryIds: 'categoryIds'
};

exports.Prisma.CourseSectionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  videoUrl: 'videoUrl',
  videoKey: 'videoKey',
  courseId: 'courseId',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  videoUrl: 'videoUrl',
  videoKey: 'videoKey',
  textContent: 'textContent',
  audioUrl: 'audioUrl',
  sectionId: 'sectionId',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserCourseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  courseId: 'courseId',
  progress: 'progress',
  completed: 'completed',
  enrolledAt: 'enrolledAt',
  completedAt: 'completedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseReviewScalarFieldEnum = {
  id: 'id',
  rating: 'rating',
  comment: 'comment',
  userId: 'userId',
  courseId: 'courseId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserLessonProgressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  lessonId: 'lessonId',
  isCompleted: 'isCompleted',
  watchedDuration: 'watchedDuration',
  completedAt: 'completedAt',
  lastAccessedAt: 'lastAccessedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSectionProgressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sectionId: 'sectionId',
  isCompleted: 'isCompleted',
  completedAt: 'completedAt',
  lastAccessedAt: 'lastAccessedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonResourceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  fileUrl: 'fileUrl',
  fileType: 'fileType',
  lessonId: 'lessonId',
  createdAt: 'createdAt'
};

exports.Prisma.PteSectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  durationMinutes: 'durationMinutes'
};

exports.Prisma.QuestionTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  pteSectionId: 'pteSectionId',
  expectedTimePerQuestion: 'expectedTimePerQuestion'
};

exports.Prisma.QuestionScalarFieldEnum = {
  id: 'id',
  questionCode: 'questionCode',
  questionTypeId: 'questionTypeId',
  testId: 'testId',
  orderInTest: 'orderInTest',
  textContent: 'textContent',
  audioUrl: 'audioUrl',
  imageUrl: 'imageUrl',
  options: 'options',
  correctAnswers: 'correctAnswers',
  wordCountMin: 'wordCountMin',
  wordCountMax: 'wordCountMax',
  durationMillis: 'durationMillis',
  originalTextWithErrors: 'originalTextWithErrors',
  incorrectWords: 'incorrectWords',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  testType: 'testType',
  totalDuration: 'totalDuration',
  isFree: 'isFree',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestAttemptScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  testId: 'testId',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  overallScore: 'overallScore',
  speakingScore: 'speakingScore',
  writingScore: 'writingScore',
  readingScore: 'readingScore',
  listeningScore: 'listeningScore',
  grammarScore: 'grammarScore',
  oralFluencyScore: 'oralFluencyScore',
  pronunciationScore: 'pronunciationScore',
  vocabularyScore: 'vocabularyScore',
  discourseScore: 'discourseScore',
  spellingScore: 'spellingScore',
  rawPteScoreJson: 'rawPteScoreJson',
  status: 'status',
  timeTakenSeconds: 'timeTakenSeconds'
};

exports.Prisma.UserResponseScalarFieldEnum = {
  id: 'id',
  testAttemptId: 'testAttemptId',
  questionId: 'questionId',
  textResponse: 'textResponse',
  audioResponseUrl: 'audioResponseUrl',
  selectedOptions: 'selectedOptions',
  orderedItems: 'orderedItems',
  highlightedWords: 'highlightedWords',
  questionScore: 'questionScore',
  isCorrect: 'isCorrect',
  aiFeedback: 'aiFeedback',
  timeTakenSeconds: 'timeTakenSeconds',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIReportScalarFieldEnum = {
  id: 'id',
  testAttemptId: 'testAttemptId',
  overallSummary: 'overallSummary',
  strengths: 'strengths',
  weaknesses: 'weaknesses',
  suggestions: 'suggestions',
  grammarScore: 'grammarScore',
  oralFluencyScore: 'oralFluencyScore',
  pronunciationScore: 'pronunciationScore',
  vocabularyScore: 'vocabularyScore',
  discourseScore: 'discourseScore',
  spellingScore: 'spellingScore',
  sectionWiseFeedback: 'sectionWiseFeedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  paymentStatus: 'paymentStatus',
  gateway: 'gateway',
  transactionId: 'transactionId',
  orderId: 'orderId',
  purchasedItem: 'purchasedItem',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  message: 'message',
  type: 'type',
  read: 'read',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN'
};

exports.AuthProviders = exports.$Enums.AuthProviders = {
  EMAIL_OTP: 'EMAIL_OTP',
  GOOGLE: 'GOOGLE',
  APPLE: 'APPLE'
};

exports.OtpType = exports.$Enums.OtpType = {
  LOGIN: 'LOGIN',
  REGISTRATION: 'REGISTRATION',
  PASSWORD_RESET: 'PASSWORD_RESET'
};

exports.SubscriptionPlan = exports.$Enums.SubscriptionPlan = {
  FREE: 'FREE',
  BASIC: 'BASIC',
  PREMIUM: 'PREMIUM'
};

exports.PteQuestionTypeName = exports.$Enums.PteQuestionTypeName = {
  READ_ALOUD: 'READ_ALOUD',
  REPEAT_SENT_ENCE: 'REPEAT_SENT_ENCE',
  DESCRIBE_IMAGE: 'DESCRIBE_IMAGE',
  RE_TELL_LECTURE: 'RE_TELL_LECTURE',
  ANSWER_SHORT_QUESTION: 'ANSWER_SHORT_QUESTION',
  SUMMARIZE_WRITTEN_TEXT: 'SUMMARIZE_WRITTEN_TEXT',
  WRITE_ESSAY: 'WRITE_ESSAY',
  READING_WRITING_FILL_IN_THE_BLANKS: 'READING_WRITING_FILL_IN_THE_BLANKS',
  MULTIPLE_CHOICE_MULTIPLE_ANSWERS_READING: 'MULTIPLE_CHOICE_MULTIPLE_ANSWERS_READING',
  RE_ORDER_PARAGRAPHS: 'RE_ORDER_PARAGRAPHS',
  READING_FILL_IN_THE_BLANKS: 'READING_FILL_IN_THE_BLANKS',
  MULTIPLE_CHOICE_SINGLE_ANSWER_READING: 'MULTIPLE_CHOICE_SINGLE_ANSWER_READING',
  SUMMARIZE_SPOKEN_TEXT: 'SUMMARIZE_SPOKEN_TEXT',
  MULTIPLE_CHOICE_MULTIPLE_ANSWERS_LISTENING: 'MULTIPLE_CHOICE_MULTIPLE_ANSWERS_LISTENING',
  LISTENING_FILL_IN_THE_BLANKS: 'LISTENING_FILL_IN_THE_BLANKS',
  HIGHLIGHT_CORRECT_SUMMARY: 'HIGHLIGHT_CORRECT_SUMMARY',
  MULTIPLE_CHOICE_SINGLE_ANSWER_LISTENING: 'MULTIPLE_CHOICE_SINGLE_ANSWER_LISTENING',
  SELECT_MISSING_WORD: 'SELECT_MISSING_WORD',
  HIGHLIGHT_INCORRECT_WORDS: 'HIGHLIGHT_INCORRECT_WORDS',
  WRITE_FROM_DICTATION: 'WRITE_FROM_DICTATION'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.Prisma.ModelName = {
  User: 'User',
  OtpCode: 'OtpCode',
  Subscription: 'Subscription',
  Course: 'Course',
  CourseSection: 'CourseSection',
  Lesson: 'Lesson',
  UserCourse: 'UserCourse',
  Category: 'Category',
  CourseReview: 'CourseReview',
  UserLessonProgress: 'UserLessonProgress',
  UserSectionProgress: 'UserSectionProgress',
  LessonResource: 'LessonResource',
  PteSection: 'PteSection',
  QuestionType: 'QuestionType',
  Question: 'Question',
  Test: 'Test',
  TestAttempt: 'TestAttempt',
  UserResponse: 'UserResponse',
  AIReport: 'AIReport',
  Transaction: 'Transaction',
  Notification: 'Notification'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
